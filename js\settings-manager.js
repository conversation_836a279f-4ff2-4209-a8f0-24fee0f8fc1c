// We use the global csvTemplates variable from csv-manager.js

// Global variable to store API keys
window.apiKeys = [];

async function initSettingsManager() {
    setupSettingsEventListeners();
    loadApiKeys();
    loadSettings();
    loadTemplates();

    const firstSubtab = document.querySelector('.settings-subtab');
    if (firstSubtab) {
        const targetId = firstSubtab.getAttribute('data-target');
        switchSettingsSubTab(targetId);
    }

    // Web worker settings have been removed as parallel processing now handles workers efficiently
}

function setupSettingsEventListeners() {
    document.getElementById('applySettingsBtn')?.addEventListener('click', saveSettings);
    document.getElementById('addTemplateBtn')?.addEventListener('click', showTemplateImport);

    // API Keys event listeners
    document.getElementById('addApiKeyBtn')?.addEventListener('click', addApiKey);
    document.getElementById('newApiKeyBtn')?.addEventListener('click', function() {
        clearApiKeyInputs();
    });
    document.getElementById('cancelEditBtn')?.addEventListener('click', function() {
        clearApiKeyInputs();
    });
    document.getElementById('deleteApiKeyBtn')?.addEventListener('click', function() {
        const apiKeysList = document.getElementById('apiKeysList');
        if (apiKeysList && apiKeysList.value) {
            const selectedIndex = parseInt(apiKeysList.value);
            if (!isNaN(selectedIndex) && selectedIndex >= 0 && selectedIndex < window.apiKeys.length) {
                showDeleteApiKeyModal(selectedIndex);
            }
        }
    });

    document.getElementById('deleteAllApiKeysBtn')?.addEventListener('click', showDeleteAllApiKeysModal);
    document.getElementById('exportApiKeysBtn')?.addEventListener('click', exportApiKeys);
    document.getElementById('importApiKeysBtn')?.addEventListener('click', function() {
        document.getElementById('apiKeysFileInput').click();
    });
    document.getElementById('apiKeysFileInput')?.addEventListener('change', importApiKeys);

    document.querySelectorAll('.settings-subtab').forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('data-target');
            switchSettingsSubTab(targetId);
        });
    });

    const descriptionSlider = document.getElementById('descriptionLengthSlider');
    if (descriptionSlider) {
        descriptionSlider.addEventListener('input', function() {
            updateDescriptionLengthDisplay(this.value);
        });
    }

    const keywordsSlider = document.getElementById('keywordsCountSlider');
    if (keywordsSlider) {
        keywordsSlider.addEventListener('input', function() {
            updateKeywordsCountDisplay(this.value);
        });
    }

    document.getElementById('concurrencySlider')?.addEventListener('input', function() {
        updateConcurrencyDisplay(this.value);
    });

    document.getElementById('rateSlider')?.addEventListener('input', function() {
        updateRateDisplay(this.value);
    });

    document.getElementById('enableParallelProcessing')?.addEventListener('change', function() {
        localStorage.setItem('csvision_enable_parallel', this.checked);
    });

    document.getElementById('enableApiKeyRotation')?.addEventListener('change', function() {
        localStorage.setItem('csvision_enable_api_key_rotation', this.checked);
    });

    document.getElementById('enableDirectVideoProcessing')?.addEventListener('change', function() {
        localStorage.setItem('csvision_use_direct_video', this.checked);
    });

    // Web worker event listeners removed as parallel processing now handles workers efficiently
}

function saveSettings() {
    // Get all settings values
    const apiKey = document.getElementById('apiKey').value.trim();
    const selectedModel = document.getElementById('modelSelect').value;
    const titleLength = document.getElementById('titleLengthSlider')?.value || '10';
    const descriptionLength = document.getElementById('descriptionLengthSlider').value;
    const keywordsCount = document.getElementById('keywordsCountSlider').value;
    const concurrency = document.getElementById('concurrencySlider')?.value || '1';
    const rate = document.getElementById('rateSlider')?.value || '1';
    const enableParallelProcessing = document.getElementById('enableParallelProcessing')?.checked || false;
    const enableApiKeyRotation = document.getElementById('enableApiKeyRotation')?.checked || false;
    const enableDirectVideoProcessing = document.getElementById('enableDirectVideoProcessing')?.checked || true;

    // Get values from the detailed prompt fields
    const positiveTitlePrompt = document.getElementById('positiveTitlePrompt')?.value.trim() || '';
    const positiveDescriptionPrompt = document.getElementById('positiveDescriptionPrompt')?.value.trim() || '';
    const positiveKeywordsPrompt = document.getElementById('positiveKeywordsPrompt')?.value.trim() || '';
    const negativeTitlePrompt = document.getElementById('negativeTitlePrompt')?.value.trim() || '';
    const negativeDescriptionPrompt = document.getElementById('negativeDescriptionPrompt')?.value.trim() || '';
    const negativeKeywordsPrompt = document.getElementById('negativeKeywordsPrompt')?.value.trim() || '';

    // For backward compatibility, combine the detailed prompts into the legacy fields
    const positivePrompt = [positiveTitlePrompt, positiveDescriptionPrompt, positiveKeywordsPrompt].filter(Boolean).join('\n\n');
    const negativePrompt = [negativeTitlePrompt, negativeDescriptionPrompt, negativeKeywordsPrompt].filter(Boolean).join('\n\n');

    // Get current stored values for comparison
    const currentSettings = {
        apiKey: localStorage.getItem('csvision_api_key') || '',
        model: localStorage.getItem('csvision_model') || 'gemini-2.0-flash',
        titleLength: localStorage.getItem('csvision_title_length') || '10',
        descriptionLength: localStorage.getItem('csvision_description_length') || '250',
        keywordsCount: localStorage.getItem('csvision_keywords_count') || '20',
        concurrency: localStorage.getItem('csvision_concurrency') || '1',
        rate: localStorage.getItem('csvision_rate') || '1',
        enableParallelProcessing: localStorage.getItem('csvision_enable_parallel') === 'true',
        enableApiKeyRotation: localStorage.getItem('csvision_enable_api_key_rotation') === 'true',
        positivePrompt: localStorage.getItem('csvision_positive_prompt') || '',
        negativePrompt: localStorage.getItem('csvision_negative_prompt') || '',
        positiveTitlePrompt: localStorage.getItem('csvision_positive_title_prompt') || '',
        positiveDescriptionPrompt: localStorage.getItem('csvision_positive_description_prompt') || '',
        positiveKeywordsPrompt: localStorage.getItem('csvision_positive_keywords_prompt') || '',
        negativeTitlePrompt: localStorage.getItem('csvision_negative_title_prompt') || '',
        negativeDescriptionPrompt: localStorage.getItem('csvision_negative_description_prompt') || '',
        negativeKeywordsPrompt: localStorage.getItem('csvision_negative_keywords_prompt') || ''
    };

    // Create and show the settings confirmation modal
    let modal = document.getElementById('settingsConfirmationModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'settingsConfirmationModal';
        modal.className = 'modal fade';
        modal.setAttribute('tabindex', '-1');
        modal.setAttribute('aria-labelledby', 'settingsConfirmationModalLabel');
        // Do not set aria-hidden to avoid accessibility issues with focused elements
        modal.innerHTML = `
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="settingsConfirmationModalLabel">
                            <i class="bi bi-gear-fill me-2"></i>Settings Confirmation
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="settingsModalBody">
                        <div class="settings-summary" id="settingsSummary">
                            <div class="alert alert-info mb-3" id="settingsAlert">
                                <i class="bi bi-info-circle me-2"></i>Review your settings changes before applying:
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card mb-3" id="apiModelCard">
                                        <div class="card-header" id="apiModelHeader">
                                            <h6 class="mb-0"><i class="bi bi-key me-2"></i>API & Model Settings</h6>
                                        </div>
                                        <div class="card-body p-3" id="apiModelBody">
                                            <div class="settings-item mb-3" id="apiKeyItem">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-key-fill me-2"></i>
                                                    <strong>API Key:</strong>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center bg-white rounded p-2" id="apiKeyValue">
                                                    <span>
                                                        ${currentSettings.apiKey ?
                                                            (window.apiKeys && window.apiKeys.find(k => k.key === currentSettings.apiKey) ?
                                                                window.apiKeys.find(k => k.key === currentSettings.apiKey).name + ': ' : '') +
                                                            '••••' + currentSettings.apiKey.slice(-4) :
                                                            'Not set'}
                                                    </span>
                                                    <i class="bi bi-arrow-right"></i>
                                                    <span>
                                                        ${apiKey ?
                                                            (window.apiKeys && window.apiKeys.find(k => k.key === apiKey) ?
                                                                window.apiKeys.find(k => k.key === apiKey).name + ': ' : '') +
                                                            '••••' + apiKey.slice(-4) :
                                                            'Not set'}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="settings-item mb-3" id="modelItem">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-cpu me-2"></i>
                                                    <strong>Model:</strong>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center bg-white rounded p-2" id="modelValue">
                                                    <span>${currentSettings.model}</span>
                                                    <i class="bi bi-arrow-right"></i>
                                                    <span>${selectedModel}</span>
                                                </div>
                                            </div>
                                            <div class="settings-item mb-3" id="descriptionLengthItem">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-text-paragraph me-2"></i>
                                                    <strong>Description Length:</strong>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center bg-white rounded p-2" id="descriptionLengthValue">
                                                    <span>${currentSettings.descriptionLength} characters</span>
                                                    <i class="bi bi-arrow-right"></i>
                                                    <span>${descriptionLength} characters</span>
                                                </div>
                                            </div>
                                            <div class="settings-item" id="keywordsCountItem">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-tags me-2"></i>
                                                    <strong>Keywords Count:</strong>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center bg-white rounded p-2" id="keywordsCountValue">
                                                    <span>${currentSettings.keywordsCount} keywords</span>
                                                    <i class="bi bi-arrow-right"></i>
                                                    <span>${keywordsCount} keywords</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card mb-3" id="processingCard">
                                        <div class="card-header" id="processingHeader">
                                            <h6 class="mb-0"><i class="bi bi-cpu me-2"></i>Processing Settings</h6>
                                        </div>
                                        <div class="card-body p-3" id="processingBody">
                                            <div class="settings-item mb-3" id="concurrencyItem">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-diagram-3 me-2"></i>
                                                    <strong>Concurrency:</strong>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center bg-white rounded p-2" id="concurrencyValue">
                                                    <span>${currentSettings.concurrency}</span>
                                                    <i class="bi bi-arrow-right"></i>
                                                    <span>${concurrency}</span>
                                                </div>
                                            </div>
                                            <div class="settings-item mb-3" id="rateItem">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-speedometer2 me-2"></i>
                                                    <strong>Rate:</strong>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center bg-white rounded p-2" id="rateValue">
                                                    <span>${currentSettings.rate}</span>
                                                    <i class="bi bi-arrow-right"></i>
                                                    <span>${rate}</span>
                                                </div>
                                            </div>
                                            <div class="settings-item mb-3" id="parallelProcessingItem">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-diagram-2 me-2"></i>
                                                    <strong>Parallel Processing:</strong>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center bg-white rounded p-2" id="parallelProcessingValue">
                                                    <span>${currentSettings.enableParallelProcessing ? 'Enabled' : 'Disabled'}</span>
                                                    <i class="bi bi-arrow-right"></i>
                                                    <span>${enableParallelProcessing ? 'Enabled' : 'Disabled'}</span>
                                                </div>
                                            </div>
                                            <div class="settings-item mb-3" id="apiKeyRotationItem">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-arrow-repeat me-2"></i>
                                                    <strong>API Key Rotation:</strong>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center bg-white rounded p-2" id="apiKeyRotationValue">
                                                    <span>${currentSettings.enableApiKeyRotation ? 'Enabled' : 'Disabled'}</span>
                                                    <i class="bi bi-arrow-right"></i>
                                                    <span>${enableApiKeyRotation ? 'Enabled' : 'Disabled'}</span>
                                                </div>
                                            </div>
                                            <!-- Web worker settings removed as parallel processing now handles workers efficiently -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card mb-3" id="promptCard">
                                        <div class="card-header" id="promptHeader">
                                            <h6 class="mb-0"><i class="bi bi-chat-square-text me-2"></i>Prompt Settings</h6>
                                        </div>
                                        <div class="card-body p-3" id="promptBody">
                                            <div class="settings-item mb-3" id="titlePromptsItem">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-type-h1 me-2"></i>
                                                    <strong>Title Prompts:</strong>
                                                </div>
                                                <div class="bg-white rounded p-2 mb-2">
                                                    <div class="small mb-1 text-success">Positive Title Prompt:</div>
                                                    <div class="border rounded p-2 mb-2">
                                                        <div class="small mb-1">Current:</div>
                                                        <div class="border rounded p-2" id="currentPositiveTitlePrompt">${currentSettings.positiveTitlePrompt || 'Not set'}</div>
                                                    </div>
                                                    <div class="border rounded p-2">
                                                        <div class="small mb-1">New:</div>
                                                        <div class="border rounded p-2" id="newPositiveTitlePrompt">${positiveTitlePrompt || 'Not set'}</div>
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded p-2">
                                                    <div class="small mb-1 text-danger">Negative Title Prompt:</div>
                                                    <div class="border rounded p-2 mb-2">
                                                        <div class="small mb-1">Current:</div>
                                                        <div class="border rounded p-2" id="currentNegativeTitlePrompt">${currentSettings.negativeTitlePrompt || 'Not set'}</div>
                                                    </div>
                                                    <div class="border rounded p-2">
                                                        <div class="small mb-1">New:</div>
                                                        <div class="border rounded p-2" id="newNegativeTitlePrompt">${negativeTitlePrompt || 'Not set'}</div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="settings-item mb-3" id="descriptionPromptsItem">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-text-paragraph me-2"></i>
                                                    <strong>Description Prompts:</strong>
                                                </div>
                                                <div class="bg-white rounded p-2 mb-2">
                                                    <div class="small mb-1 text-success">Positive Description Prompt:</div>
                                                    <div class="border rounded p-2 mb-2">
                                                        <div class="small mb-1">Current:</div>
                                                        <div class="border rounded p-2" id="currentPositiveDescriptionPrompt">${currentSettings.positiveDescriptionPrompt || 'Not set'}</div>
                                                    </div>
                                                    <div class="border rounded p-2">
                                                        <div class="small mb-1">New:</div>
                                                        <div class="border rounded p-2" id="newPositiveDescriptionPrompt">${positiveDescriptionPrompt || 'Not set'}</div>
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded p-2">
                                                    <div class="small mb-1 text-danger">Negative Description Prompt:</div>
                                                    <div class="border rounded p-2 mb-2">
                                                        <div class="small mb-1">Current:</div>
                                                        <div class="border rounded p-2" id="currentNegativeDescriptionPrompt">${currentSettings.negativeDescriptionPrompt || 'Not set'}</div>
                                                    </div>
                                                    <div class="border rounded p-2">
                                                        <div class="small mb-1">New:</div>
                                                        <div class="border rounded p-2" id="newNegativeDescriptionPrompt">${negativeDescriptionPrompt || 'Not set'}</div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="settings-item mb-3" id="keywordsPromptsItem">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-tags me-2"></i>
                                                    <strong>Keywords Prompts:</strong>
                                                </div>
                                                <div class="bg-white rounded p-2 mb-2">
                                                    <div class="small mb-1 text-success">Positive Keywords Prompt:</div>
                                                    <div class="border rounded p-2 mb-2">
                                                        <div class="small mb-1">Current:</div>
                                                        <div class="border rounded p-2" id="currentPositiveKeywordsPrompt">${currentSettings.positiveKeywordsPrompt || 'Not set'}</div>
                                                    </div>
                                                    <div class="border rounded p-2">
                                                        <div class="small mb-1">New:</div>
                                                        <div class="border rounded p-2" id="newPositiveKeywordsPrompt">${positiveKeywordsPrompt || 'Not set'}</div>
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded p-2">
                                                    <div class="small mb-1 text-danger">Negative Keywords Prompt:</div>
                                                    <div class="border rounded p-2 mb-2">
                                                        <div class="small mb-1">Current:</div>
                                                        <div class="border rounded p-2" id="currentNegativeKeywordsPrompt">${currentSettings.negativeKeywordsPrompt || 'Not set'}</div>
                                                    </div>
                                                    <div class="border rounded p-2">
                                                        <div class="small mb-1">New:</div>
                                                        <div class="border rounded p-2" id="newNegativeKeywordsPrompt">${negativeKeywordsPrompt || 'Not set'}</div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="settings-item" id="legacyPromptsItem">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-chat-square-text me-2"></i>
                                                    <strong>Legacy Prompts (Combined):</strong>
                                                </div>
                                                <div class="bg-white rounded p-2 mb-2">
                                                    <div class="small mb-1 text-success">Positive Prompt (Combined):</div>
                                                    <div class="border rounded p-2 mb-2">
                                                        <div class="small mb-1">Current:</div>
                                                        <div class="border rounded p-2" id="currentPositivePrompt">${currentSettings.positivePrompt || 'Not set'}</div>
                                                    </div>
                                                    <div class="border rounded p-2">
                                                        <div class="small mb-1">New:</div>
                                                        <div class="border rounded p-2" id="newPositivePrompt">${positivePrompt || 'Not set'}</div>
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded p-2">
                                                    <div class="small mb-1 text-danger">Negative Prompt (Combined):</div>
                                                    <div class="border rounded p-2 mb-2">
                                                        <div class="small mb-1">Current:</div>
                                                        <div class="border rounded p-2" id="currentNegativePrompt">${currentSettings.negativePrompt || 'Not set'}</div>
                                                    </div>
                                                    <div class="border rounded p-2">
                                                        <div class="small mb-1">New:</div>
                                                        <div class="border rounded p-2" id="newNegativePrompt">${negativePrompt || 'Not set'}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card mb-3" id="templateCard">
                                        <div class="card-header" id="templateHeader">
                                            <h6 class="mb-0"><i class="bi bi-file-earmark-text me-2"></i>Template Settings</h6>
                                        </div>
                                        <div class="card-body p-3" id="templateBody">
                                            <div class="settings-item" id="templateCountItem">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-file-earmark-text me-2"></i>
                                                    <strong>Available Templates:</strong>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center bg-white rounded p-2" id="templateCountValue">
                                                    <span>${window.csvTemplates?.length || 0} templates</span>
                                                    <i class="bi bi-check-circle text-success"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer" id="settingsModalFooter">
                        <button type="button" class="btn btn-secondary" id="cancelSettingsBtn" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-2"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-primary" id="confirmSettingsBtn">
                            <i class="bi bi-check-circle me-2"></i>Apply Settings
                        </button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);

        // Add event listener for the confirm button
        document.getElementById('confirmSettingsBtn').addEventListener('click', function() {
            // Save all settings
            localStorage.setItem('csvision_api_key', apiKey);
            localStorage.setItem('csvision_model', selectedModel);
            localStorage.setItem('csvision_title_length', titleLength);
            localStorage.setItem('csvision_description_length', descriptionLength);
            localStorage.setItem('csvision_keywords_count', keywordsCount);
            localStorage.setItem('csvision_concurrency', concurrency);
            localStorage.setItem('csvision_rate', rate);
            localStorage.setItem('csvision_enable_parallel', enableParallelProcessing);
            localStorage.setItem('csvision_enable_api_key_rotation', enableApiKeyRotation);
            localStorage.setItem('csvision_use_direct_video', enableDirectVideoProcessing);

            // Save detailed prompts
            localStorage.setItem('csvision_positive_title_prompt', positiveTitlePrompt);
            localStorage.setItem('csvision_positive_description_prompt', positiveDescriptionPrompt);
            localStorage.setItem('csvision_positive_keywords_prompt', positiveKeywordsPrompt);
            localStorage.setItem('csvision_negative_title_prompt', negativeTitlePrompt);
            localStorage.setItem('csvision_negative_description_prompt', negativeDescriptionPrompt);
            localStorage.setItem('csvision_negative_keywords_prompt', negativeKeywordsPrompt);

            // Save legacy prompts for backward compatibility
            localStorage.setItem('csvision_positive_prompt', positivePrompt);
            localStorage.setItem('csvision_negative_prompt', negativePrompt);

            // Save the currently selected prompt as the applied prompt
            var promptList = document.getElementById('promptList');
            if (promptList && promptList.selectedIndex >= 0) {
                var selectedPromptName = promptList.options[promptList.selectedIndex].value;
                if (selectedPromptName && typeof window.saveAppliedPromptName === 'function') {
                    window.saveAppliedPromptName(selectedPromptName);
                    console.log('Saved applied prompt from settings:', selectedPromptName);
                }
            }

            // Close the modal
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }

            // Show success toast
            showToast('Settings applied successfully', 'success');
        });
    }

    // Create the modal instance with proper accessibility options
    const bsModal = new bootstrap.Modal(modal, {
        backdrop: true,
        keyboard: true,
        focus: true
    });

    // Add event listener for when the modal is fully shown
    modal.addEventListener('shown.bs.modal', function() {
        // Focus on the confirm button after modal is fully shown
        setTimeout(() => {
            const confirmBtn = document.getElementById('confirmSettingsBtn');
            if (confirmBtn) {
                confirmBtn.focus();
            }
        }, 100);
    }, { once: true });

    // Show the modal
    bsModal.show();
}

function loadSettings() {
    // API & Model Settings
    const apiKey = localStorage.getItem('csvision_api_key') || '';
    const selectedModel = localStorage.getItem('csvision_model') || 'gemini-2.0-flash';
    const titleLength = localStorage.getItem('csvision_title_length') || '10';
    const descriptionLength = localStorage.getItem('csvision_description_length') || '250';
    const keywordsCount = localStorage.getItem('csvision_keywords_count') || '20';

    // Processing Settings
    const concurrency = localStorage.getItem('csvision_concurrency') || '1';
    const rate = localStorage.getItem('csvision_rate') || '1';
    const enableParallelProcessing = localStorage.getItem('csvision_enable_parallel') === 'true';
    const enableApiKeyRotation = localStorage.getItem('csvision_enable_api_key_rotation') === 'true';
    const enableDirectVideoProcessing = localStorage.getItem('csvision_use_direct_video') !== 'false';

    // Initialize API key failure tracking if not already initialized
    if (!window.apiKeyFailures) {
        window.apiKeyFailures = {};
    }

    // Detailed Prompt Settings
    const positiveTitlePrompt = localStorage.getItem('csvision_positive_title_prompt') || '';
    const positiveDescriptionPrompt = localStorage.getItem('csvision_positive_description_prompt') || '';
    const positiveKeywordsPrompt = localStorage.getItem('csvision_positive_keywords_prompt') || '';
    const negativeTitlePrompt = localStorage.getItem('csvision_negative_title_prompt') || '';
    const negativeDescriptionPrompt = localStorage.getItem('csvision_negative_description_prompt') || '';
    const negativeKeywordsPrompt = localStorage.getItem('csvision_negative_keywords_prompt') || '';

    // Legacy Prompt Settings
    const positivePrompt = localStorage.getItem('csvision_positive_prompt') || '';
    const negativePrompt = localStorage.getItem('csvision_negative_prompt') || '';

    // Set values to form elements
    // Safely set values to form elements
    const setElementValue = (id, value) => {
        const element = document.getElementById(id);
        if (element) element.value = value;
    };

    const setElementChecked = (id, checked) => {
        const element = document.getElementById(id);
        if (element) element.checked = checked;
    };

    setElementValue('apiKey', apiKey);
    setElementValue('modelSelect', selectedModel);
    setElementValue('titleLengthSlider', titleLength);
    setElementValue('descriptionLengthSlider', descriptionLength);
    setElementValue('keywordsCountSlider', keywordsCount);
    setElementValue('concurrencySlider', concurrency);
    setElementValue('rateSlider', rate);
    setElementChecked('enableParallelProcessing', enableParallelProcessing);
    setElementChecked('enableApiKeyRotation', enableApiKeyRotation);
    setElementChecked('enableDirectVideoProcessing', enableDirectVideoProcessing);

    // Set detailed prompt values
    setElementValue('positiveTitlePrompt', positiveTitlePrompt || (positivePrompt ? splitPrompt(positivePrompt, 0) : ''));
    setElementValue('positiveDescriptionPrompt', positiveDescriptionPrompt || (positivePrompt ? splitPrompt(positivePrompt, 1) : ''));
    setElementValue('positiveKeywordsPrompt', positiveKeywordsPrompt || (positivePrompt ? splitPrompt(positivePrompt, 2) : ''));
    setElementValue('negativeTitlePrompt', negativeTitlePrompt || (negativePrompt ? splitPrompt(negativePrompt, 0) : ''));
    setElementValue('negativeDescriptionPrompt', negativeDescriptionPrompt || (negativePrompt ? splitPrompt(negativePrompt, 1) : ''));
    setElementValue('negativeKeywordsPrompt', negativeKeywordsPrompt || (negativePrompt ? splitPrompt(negativePrompt, 2) : ''));

    // Set legacy prompt values
    setElementValue('positivePrompt', positivePrompt);
    setElementValue('negativePrompt', negativePrompt);

    // Update displays
    try {
        // Update title length display
        const titleLengthValue = document.getElementById('titleLengthValue');
        if (titleLengthValue) titleLengthValue.textContent = titleLength;

        updateDescriptionLengthDisplay(descriptionLength);
        updateKeywordsCountDisplay(keywordsCount);
        updateConcurrencyDisplay(concurrency);
        updateRateDisplay(rate);
    } catch (error) {
        console.warn('Error updating displays:', error);
    }
}

// Helper function to split a prompt into parts
function splitPrompt(prompt, index) {
    if (!prompt) return '';
    const parts = prompt.split('\n\n');
    if (parts.length > index) {
        return parts[index];
    }
    return prompt; // Return the whole prompt if it can't be split
}

function updateDescriptionLengthDisplay(value) {
    const displayElement = document.getElementById('descriptionLengthValue');
    if (displayElement) {
        displayElement.textContent = value;
    }
}

function updateKeywordsCountDisplay(value) {
    const displayElement = document.getElementById('keywordsCountValue');
    if (displayElement) {
        displayElement.textContent = value;
    }
}

function updateConcurrencyDisplay(value) {
    const displayElement = document.getElementById('concurrencyValue');
    if (displayElement) {
        displayElement.textContent = value;
    }
}

function updateRateDisplay(value) {
    const displayElement = document.getElementById('rateValue');
    if (displayElement) {
        displayElement.textContent = value;
    }
}

// updateWorkerStatus function removed as web worker option has been removed

// Function to mark an API key as failed
function markApiKeyAsFailed(apiKey) {
    if (!window.apiKeyFailures) {
        window.apiKeyFailures = {};
    }

    // Mark the key as failed with a timestamp
    window.apiKeyFailures[apiKey] = Date.now();

    // Find the API key name
    let apiKeyName = "Unknown";
    let apiKeyIndex = -1;
    if (window.apiKeys && window.apiKeys.length > 0) {
        apiKeyIndex = window.apiKeys.findIndex(key => key.key === apiKey);
        if (apiKeyIndex >= 0) {
            apiKeyName = window.apiKeys[apiKeyIndex].name;
        }
    }

    console.log(`API key ${apiKeyName} (••••${apiKey.slice(-4)}) marked as failed`);

    // Check if API key rotation is enabled
    const enableApiKeyRotation = localStorage.getItem('csvision_enable_api_key_rotation') === 'true';

    // If rotation is enabled, find the next key
    if (enableApiKeyRotation && window.apiKeys && window.apiKeys.length > 1) {
        // Find the next available key
        let nextKeyIndex = -1;
        for (let i = 1; i <= window.apiKeys.length; i++) {
            const index = (apiKeyIndex + i) % window.apiKeys.length;
            if (!isApiKeyFailed(window.apiKeys[index].key)) {
                nextKeyIndex = index;
                break;
            }
        }

        if (nextKeyIndex >= 0) {
            const nextKey = window.apiKeys[nextKeyIndex];
            // Show a toast notification with details
            showToast(`API key "${apiKeyName}" failed, switching to "${nextKey.name}"...`, 'warning');
        } else {
            // All keys are failed
            showToast(`API key "${apiKeyName}" failed. All API keys have failed recently.`, 'error');
        }
    } else {
        // Rotation is not enabled or only one key available
        showToast(`API key "${apiKeyName}" failed.`, 'warning');
    }
}

// Function to check if an API key is marked as failed
function isApiKeyFailed(apiKey) {
    if (!window.apiKeyFailures || !window.apiKeyFailures[apiKey]) {
        return false;
    }

    // Check if the failure was within the last 5 minutes
    const failureTime = window.apiKeyFailures[apiKey];
    const now = Date.now();
    const fiveMinutesInMs = 5 * 60 * 1000;

    // If the failure was more than 5 minutes ago, reset the failure status
    if (now - failureTime > fiveMinutesInMs) {
        delete window.apiKeyFailures[apiKey];
        return false;
    }

    return true;
}

// Function to get the next API key in rotation
function getNextApiKey() {
    if (!window.apiKeys || window.apiKeys.length === 0) {
        return null;
    }

    // Find the current active key index
    const activeKeyIndex = window.apiKeys.findIndex(key => key.active);

    // If no active key, return the first non-failed key
    if (activeKeyIndex === -1) {
        for (let i = 0; i < window.apiKeys.length; i++) {
            if (!isApiKeyFailed(window.apiKeys[i].key)) {
                // Set this key as active
                window.apiKeys.forEach(key => key.active = false);
                window.apiKeys[i].active = true;
                saveApiKeys();
                updateApiKeysList();

                return window.apiKeys[i].key;
            }
        }
        return null; // All keys are failed
    }

    // Try to find the next non-failed key in rotation
    for (let i = 1; i <= window.apiKeys.length; i++) {
        const nextIndex = (activeKeyIndex + i) % window.apiKeys.length;
        if (!isApiKeyFailed(window.apiKeys[nextIndex].key)) {
            // Get the current and next key names for logging
            const currentKeyName = window.apiKeys[activeKeyIndex].name;
            const nextKeyName = window.apiKeys[nextIndex].name;
            console.log(`Rotating from API key "${currentKeyName}" to "${nextKeyName}"`);

            // Update the active key
            window.apiKeys.forEach(key => key.active = false);
            window.apiKeys[nextIndex].active = true;
            saveApiKeys();

            // Update the UI to show the new active key
            updateApiKeysList();

            // If the settings tab is open, update the API key input field
            const apiKeyInput = document.getElementById('apiKey');
            if (apiKeyInput) {
                apiKeyInput.value = window.apiKeys[nextIndex].key;
            }

            return window.apiKeys[nextIndex].key;
        }
    }

    // If all keys are failed, return null
    return null;
}

function getApiKey() {
    // First, check if we have an active API key in our apiKeys array
    if (window.apiKeys && window.apiKeys.length > 0) {
        const activeKey = window.apiKeys.find(key => key.active);
        if (activeKey && !isApiKeyFailed(activeKey.key)) {
            return activeKey.key;
        }

        // If rotation is enabled and the active key is failed, try to get the next key
        const enableApiKeyRotation = localStorage.getItem('csvision_enable_api_key_rotation') === 'true';
        if (enableApiKeyRotation) {
            const nextKey = getNextApiKey();
            if (nextKey) {
                return nextKey;
            }
        }
    }

    // Fallback to legacy method
    let apiKey = "";
    const apiKeyInput = document.getElementById('apiKeyInput');
    const apiKeyElement = document.getElementById('apiKey');
    if (apiKeyInput && apiKeyInput.value.trim()) {
        apiKey = apiKeyInput.value.trim();
    } else if (apiKeyElement && apiKeyElement.value.trim()) {
        apiKey = apiKeyElement.value.trim();
    }

    // If we found a key using the legacy method, try to add it to our apiKeys array
    if (apiKey && (!window.apiKeys || window.apiKeys.length === 0)) {
        window.apiKeys = [{
            name: 'Default API Key',
            key: apiKey,
            active: true,
            dateAdded: new Date().toISOString()
        }];
        saveApiKeys();
        updateApiKeysList();
    }

    return apiKey;
}

function getPositivePrompt() {
    const positivePromptInput = document.getElementById('positivePrompt');
    const storedPrompt = localStorage.getItem('csvision_positive_prompt');
    return positivePromptInput?.value.trim() || storedPrompt ||
           "Describe this image in detail and provide a list of relevant keywords for SEO purposes.";
}

function getNegativePrompt() {
    const negativePromptInput = document.getElementById('negativePrompt');
    const storedPrompt = localStorage.getItem('csvision_negative_prompt');
    return negativePromptInput?.value.trim() || storedPrompt || "";
}

// Detailed prompt getters
function getPositiveTitlePrompt() {
    const input = document.getElementById('positiveTitlePrompt');
    const storedPrompt = localStorage.getItem('csvision_positive_title_prompt');
    return input?.value.trim() || storedPrompt ||
           "Create a concise, descriptive title that accurately represents the main subject or theme of this image.";
}

function getNegativeTitlePrompt() {
    const input = document.getElementById('negativeTitlePrompt');
    const storedPrompt = localStorage.getItem('csvision_negative_title_prompt');
    return input?.value.trim() || storedPrompt || "";
}

function getPositiveDescriptionPrompt() {
    const input = document.getElementById('positiveDescriptionPrompt');
    const storedPrompt = localStorage.getItem('csvision_positive_description_prompt');
    return input?.value.trim() || storedPrompt ||
           "Provide a detailed description of this image, including the main subject, setting, colors, mood, and any notable elements or actions.";
}

function getNegativeDescriptionPrompt() {
    const input = document.getElementById('negativeDescriptionPrompt');
    const storedPrompt = localStorage.getItem('csvision_negative_description_prompt');
    return input?.value.trim() || storedPrompt || "";
}

function getPositiveKeywordsPrompt() {
    const input = document.getElementById('positiveKeywordsPrompt');
    const storedPrompt = localStorage.getItem('csvision_positive_keywords_prompt');
    return input?.value.trim() || storedPrompt ||
           "Generate relevant keywords that accurately describe the content, subject, style, and context of this image for SEO purposes.";
}

function getNegativeKeywordsPrompt() {
    const input = document.getElementById('negativeKeywordsPrompt');
    const storedPrompt = localStorage.getItem('csvision_negative_keywords_prompt');
    return input?.value.trim() || storedPrompt || "";
}

function getMaxTitleLength() {
    const slider = document.getElementById('titleLengthSlider');
    return slider ? parseInt(slider.value) : 10;
}

function getMaxDescriptionLength() {
    const slider = document.getElementById('descriptionLengthSlider');
    return slider ? parseInt(slider.value) : 250;
}

function getMaxKeywordsFromSettings() {
    const slider = document.getElementById('keywordsCountSlider');
    return slider ? parseInt(slider.value) : 20;
}

function showTemplateImport() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv';
    input.style.display = 'none';
    input.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            importTemplate(e.target.files[0]);
        }
    });
    document.body.appendChild(input);
    input.click();
    document.body.removeChild(input);
}

function saveTemplates() {
    // csvTemplates is a global variable defined in csv-manager.js
    localStorage.setItem('csvision_templates', JSON.stringify(window.csvTemplates));
}

function loadTemplates() {
    const savedTemplates = localStorage.getItem('csvision_templates');
    if (savedTemplates) {
        // csvTemplates is a global variable defined in csv-manager.js
        window.csvTemplates = JSON.parse(savedTemplates);

        if (typeof updateTemplateList === 'function') {
            updateTemplateList();
        }

        if (typeof updateTemplateButtons === 'function') {
            updateTemplateButtons();
        }
    }
}

function updateTemplateButtons() {
    const existingButtons = document.querySelectorAll('.template-export-btn');
    existingButtons.forEach(btn => btn.remove());
    const btnGroup = document.querySelector('#content .btn-group');
    // csvTemplates is a global variable defined in csv-manager.js
    if (btnGroup && window.csvTemplates && window.csvTemplates.length > 0) {
        const hasData = Array.isArray(window.metadataResults) && window.metadataResults.length > 0;
        window.csvTemplates.forEach(template => {
            const btn = document.createElement('button');
            btn.className = 'btn btn-info template-export-btn bi bi-download';
            btn.disabled = !hasData;
            btn.innerHTML = ` Export to ${template.name}`;
            btn.addEventListener('click', () => exportToTemplate(template));
            btnGroup.appendChild(btn);
        });
    }
}

function updateTemplateList() {
    let templateList = document.getElementById('templateList');
    if (!templateList) {
        templateList = document.createElement('div');
        templateList.id = 'templateList';
        templateList.className = 'list-group mt-2';
        const addTemplateBtn = document.getElementById('addTemplateBtn');
        if (addTemplateBtn) {
            addTemplateBtn.parentNode.appendChild(templateList);
        }
    }
    templateList.innerHTML = '';

    // csvTemplates is a global variable defined in csv-manager.js
    if (!window.csvTemplates || window.csvTemplates.length === 0) {
        templateList.innerHTML = '<div class="list-group-item text-center text-muted">No templates available</div>';
        return;
    }

    window.csvTemplates.forEach((template, index) => {
        const item = document.createElement('div');
        item.className = 'list-group-item d-flex justify-content-between align-items-center';

        const nameSpan = document.createElement('span');
        nameSpan.textContent = template.name;

        const fieldsCount = document.createElement('span');
        fieldsCount.className = 'badge bg-primary rounded-pill';
        fieldsCount.textContent = template.fields ? template.fields.length : 0;

        const btnGroup = document.createElement('div');
        btnGroup.className = 'btn-group';

        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'btn btn-sm btn-outline-danger bi bi-trash';
        deleteBtn.title = 'Delete template';
        deleteBtn.addEventListener('click', () => deleteTemplate(index));

        btnGroup.appendChild(deleteBtn);
        item.appendChild(nameSpan);
        item.appendChild(fieldsCount);
        item.appendChild(btnGroup);
        templateList.appendChild(item);
    });
}

function switchSettingsSubTab(targetId) {
    document.querySelectorAll('.settings-subcontent').forEach(content => {
        content.classList.remove('active');
    });

    document.querySelectorAll('.settings-subtab').forEach(tab => {
        tab.classList.remove('active');
    });

    const targetContent = document.getElementById(targetId);
    if (targetContent) {
        targetContent.classList.add('active');
    }

    const activeTab = document.querySelector(`[data-target="${targetId}"]`);
    if (activeTab) {
        activeTab.classList.add('active');
    }
}

// updateWorkerStatus function removed as web worker option has been removed

function getSelectedModel() {
    const modelSelect = document.getElementById('modelSelect');
    return modelSelect ? modelSelect.value : 'gemini-2.0-flash';
}

function initializeSettings() {
    // Set default values if not already set
    const defaults = {
        // Legacy prompts
        'csvision_positive_prompt': "Describe this image in detail and provide relevant keywords. " +
                                  "Format the response as: DESCRIPTION: (detailed description) KEYWORDS: (comma-separated keywords)",
        'csvision_negative_prompt': '',

        // Detailed prompts
        'csvision_positive_title_prompt': "Create a concise, descriptive title that accurately represents the main subject or theme of this image.",
        'csvision_positive_description_prompt': "Provide a detailed description of this image, including the main subject, setting, colors, mood, and any notable elements or actions.",
        'csvision_positive_keywords_prompt': "Generate relevant keywords that accurately describe the content, subject, style, and context of this image for SEO purposes.",
        'csvision_negative_title_prompt': "Avoid generic, vague, or misleading titles. Don't use clickbait language or excessive punctuation.",
        'csvision_negative_description_prompt': "Avoid vague descriptions, technical jargon, or made-up details. Don't include watermarks or elements not actually present in the image.",
        'csvision_negative_keywords_prompt': "Avoid irrelevant, overly generic, or misleading keywords. Don't include brand names unless they are clearly visible in the image.",

        // Model and length settings
        'csvision_model': 'gemini-2.0-flash',
        'csvision_title_length': '10',
        'csvision_description_length': '250',
        'csvision_keywords_count': '20',

        // Processing settings
        'csvision_concurrency': '3', // Default to 3 concurrent processes
        'csvision_rate': '5', // Default to 5 requests per second
        'csvision_enable_parallel': 'true', // Enable parallel processing by default
        'csvision_enable_api_key_rotation': 'false', // Disable API key rotation by default
        'csvision_use_direct_video': 'true' // Enable direct video processing by default
    };

    // Set default values for any missing settings
    Object.entries(defaults).forEach(([key, value]) => {
        if (!localStorage.getItem(key)) {
            localStorage.setItem(key, value);
        }
    });

    // Load all settings
    loadSettings();
}

// API Keys Management Functions
function loadApiKeys() {
    const savedApiKeys = localStorage.getItem('csvision_api_keys');
    if (savedApiKeys) {
        try {
            window.apiKeys = JSON.parse(savedApiKeys);
            console.log('Loaded API keys:', window.apiKeys.length);
        } catch (error) {
            console.error('Error parsing saved API keys:', error);
            window.apiKeys = [];
        }
    } else {
        // If no saved API keys, initialize with empty array
        window.apiKeys = [];

        // Check if there's a legacy API key to migrate
        const legacyApiKey = localStorage.getItem('csvision_api_key');
        if (legacyApiKey && legacyApiKey.trim() !== '') {
            // Migrate the legacy API key
            window.apiKeys.push({
                name: 'Default API Key',
                key: legacyApiKey,
                active: true,
                dateAdded: new Date().toISOString()
            });
            saveApiKeys();
            console.log('Migrated legacy API key');
        }
    }

    updateApiKeysList();
}

function saveApiKeys() {
    localStorage.setItem('csvision_api_keys', JSON.stringify(window.apiKeys));
    console.log('Saved API keys:', window.apiKeys.length);
}

function addApiKey() {
    const apiKeyInput = document.getElementById('apiKey');
    const apiKeyNameInput = document.getElementById('apiKeyName');
    const addApiKeyBtn = document.getElementById('addApiKeyBtn');

    const apiKey = apiKeyInput.value.trim();
    let apiKeyName = apiKeyNameInput.value.trim();

    if (!apiKey) {
        showToast('Please enter an API key', 'error');
        return;
    }

    if (!apiKeyName) {
        apiKeyName = 'API Key ' + (window.apiKeys.length + 1);
    }

    // Check if we're in update mode
    const isUpdateMode = addApiKeyBtn && addApiKeyBtn.dataset.mode === 'update';
    const updateIndex = isUpdateMode ? parseInt(addApiKeyBtn.dataset.index) : -1;

    if (isUpdateMode && (updateIndex >= 0 && updateIndex < window.apiKeys.length)) {
        // We're updating an existing key
        const wasActive = window.apiKeys[updateIndex].active;

        // Check if this key already exists elsewhere
        const existingKeyIndex = window.apiKeys.findIndex(k => k.key === apiKey && k !== window.apiKeys[updateIndex]);
        if (existingKeyIndex >= 0) {
            showToast('This API key already exists', 'error');
            return;
        }

        // Update the key
        window.apiKeys[updateIndex].name = apiKeyName;
        window.apiKeys[updateIndex].key = apiKey;

        // If this was the active key, update the current API key in localStorage
        if (wasActive) {
            localStorage.setItem('csvision_api_key', apiKey);
        }

        // Save and update UI
        saveApiKeys();
        updateApiKeysList();

        // Clear inputs and reset button
        clearApiKeyInputs();

        showToast('API key updated successfully', 'success');
    } else {
        // We're adding a new key

        // Check if this key already exists
        const existingKeyIndex = window.apiKeys.findIndex(k => k.key === apiKey);
        if (existingKeyIndex >= 0) {
            showToast('This API key already exists', 'error');
            return;
        }

        // Add the new key
        window.apiKeys.push({
            name: apiKeyName,
            key: apiKey,
            active: window.apiKeys.length === 0, // Make active if it's the first key
            dateAdded: new Date().toISOString()
        });

        // If this is the first key, make sure it's set as the current API key
        if (window.apiKeys.length === 1) {
            localStorage.setItem('csvision_api_key', apiKey);
        }

        // Save and update UI
        saveApiKeys();
        updateApiKeysList();

        // Clear inputs
        clearApiKeyInputs();

        showToast('API key added successfully', 'success');
    }
}

function setActiveApiKey(index) {
    if (index < 0 || index >= window.apiKeys.length) {
        console.error('Invalid API key index:', index);
        return;
    }

    // Set all keys to inactive
    window.apiKeys.forEach(key => key.active = false);

    // Set the selected key to active
    window.apiKeys[index].active = true;

    // Update the current API key in localStorage
    localStorage.setItem('csvision_api_key', window.apiKeys[index].key);

    // Save and update UI
    saveApiKeys();
    updateApiKeysList();

    showToast(`API key "${window.apiKeys[index].name}" is now active`, 'success');
}

function ensureDeleteApiKeyModal() {
    let modalElement = document.getElementById('deleteApiKeyModal');

    if (!modalElement) {
        console.log('Creating Delete API Key modal element');

        // Create the modal element
        const modalHTML = `
        <div class="modal fade" id="deleteApiKeyModal" tabindex="-1" aria-labelledby="deleteApiKeyModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title" id="deleteApiKeyModalLabel">Confirm Delete API Key</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete this API key?</p>
                        <p class="fw-bold" id="apiKeyToDeleteName"></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="cancelDeleteApiKey">Cancel</button>
                        <button type="button" class="btn btn-danger" id="confirmDeleteApiKey">Delete</button>
                    </div>
                </div>
            </div>
        </div>`;

        // Add the modal to the document
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Get the newly created modal element
        modalElement = document.getElementById('deleteApiKeyModal');
    }

    return modalElement;
}

function ensureDeleteAllApiKeysModal() {
    let modalElement = document.getElementById('deleteAllApiKeysModal');

    if (!modalElement) {
        console.log('Creating Delete All API Keys modal element');

        // Create the modal element
        const modalHTML = `
        <div class="modal fade" id="deleteAllApiKeysModal" tabindex="-1" aria-labelledby="deleteAllApiKeysModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title" id="deleteAllApiKeysModalLabel">Confirm Delete All API Keys</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete all API keys?</p>
                        <p class="text-danger"><strong>This action cannot be undone!</strong></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="cancelDeleteAllApiKeys">Cancel</button>
                        <button type="button" class="btn btn-danger" id="confirmDeleteAllApiKeys">Delete All</button>
                    </div>
                </div>
            </div>
        </div>`;

        // Add the modal to the document
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Get the newly created modal element
        modalElement = document.getElementById('deleteAllApiKeysModal');
    }

    return modalElement;
}

function showDeleteApiKeyModal(index) {
    if (index < 0 || index >= window.apiKeys.length) {
        console.error('Invalid API key index:', index);
        return;
    }

    const apiKey = window.apiKeys[index];
    const modalElement = ensureDeleteApiKeyModal();

    if (!modalElement) {
        console.error('Failed to create Delete API Key modal element');
        // Fallback to confirm dialog
        if (confirm(`Delete API key "${apiKey.name}"?`)) {
            deleteApiKey(index);
        }
        return;
    }

    const apiKeyToDeleteName = document.getElementById('apiKeyToDeleteName');
    if (apiKeyToDeleteName) {
        apiKeyToDeleteName.textContent = `${apiKey.name} (••••${apiKey.key.slice(-4)})`;
    }

    // Set up confirm button
    const confirmDeleteApiKey = document.getElementById('confirmDeleteApiKey');
    if (confirmDeleteApiKey) {
        // Remove any existing event listeners
        const newConfirmBtn = confirmDeleteApiKey.cloneNode(true);
        confirmDeleteApiKey.parentNode.replaceChild(newConfirmBtn, confirmDeleteApiKey);

        // Add new event listener with the current index
        newConfirmBtn.addEventListener('click', function() {
            deleteApiKey(index);
            try {
                const bsModal = bootstrap.Modal.getInstance(modalElement);
                if (bsModal) {
                    bsModal.hide();
                }
            } catch (error) {
                console.error('Error hiding modal:', error);
                modalElement.classList.remove('show');
                modalElement.style.display = 'none';
                document.body.classList.remove('modal-open');
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) backdrop.remove();
            }
        }, { once: true });
    }

    // Set up cancel button
    const cancelDeleteApiKey = document.getElementById('cancelDeleteApiKey');
    if (cancelDeleteApiKey) {
        // Remove any existing event listeners
        const newCancelBtn = cancelDeleteApiKey.cloneNode(true);
        cancelDeleteApiKey.parentNode.replaceChild(newCancelBtn, cancelDeleteApiKey);

        // Add new event listener
        newCancelBtn.addEventListener('click', function() {
            try {
                const bsModal = bootstrap.Modal.getInstance(modalElement);
                if (bsModal) {
                    bsModal.hide();
                }
            } catch (error) {
                console.error('Error hiding modal:', error);
                modalElement.classList.remove('show');
                modalElement.style.display = 'none';
                document.body.classList.remove('modal-open');
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) backdrop.remove();
            }
        }, { once: true });
    }

    // Show the modal
    try {
        // Make sure Bootstrap is available
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            // Create and show the modal
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } else {
            console.error('Bootstrap Modal is not available');
            alert(`Are you sure you want to delete API key "${apiKey.name}"?`);
            if (confirm(`Delete API key "${apiKey.name}"?`)) {
                deleteApiKey(index);
            }
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        // Fallback to confirm dialog
        if (confirm(`Delete API key "${apiKey.name}"?`)) {
            deleteApiKey(index);
        }
    }
}

function deleteApiKey(index) {
    if (index < 0 || index >= window.apiKeys.length) {
        console.error('Invalid API key index:', index);
        return;
    }

    const wasActive = window.apiKeys[index].active;
    const keyName = window.apiKeys[index].name;

    // Remove the key
    window.apiKeys.splice(index, 1);

    // If the deleted key was active and we have other keys, make the first one active
    if (wasActive && window.apiKeys.length > 0) {
        window.apiKeys[0].active = true;
        localStorage.setItem('csvision_api_key', window.apiKeys[0].key);
    } else if (window.apiKeys.length === 0) {
        // If no keys left, clear the current API key
        localStorage.removeItem('csvision_api_key');
    }

    // Save and update UI
    saveApiKeys();
    updateApiKeysList();

    showToast(`API key "${keyName}" deleted`, 'success');
}

function showDeleteAllApiKeysModal() {
    if (!window.apiKeys || window.apiKeys.length === 0) {
        showToast('No API keys to delete', 'info');
        return;
    }

    const modalElement = ensureDeleteAllApiKeysModal();

    if (!modalElement) {
        console.error('Failed to create Delete All API Keys modal element');
        // Fallback to confirm dialog
        if (confirm('Are you sure you want to delete all API keys? This action cannot be undone.')) {
            deleteAllApiKeys();
        }
        return;
    }

    // Set up confirm button
    const confirmDeleteAllApiKeys = document.getElementById('confirmDeleteAllApiKeys');
    if (confirmDeleteAllApiKeys) {
        // Remove any existing event listeners
        const newConfirmBtn = confirmDeleteAllApiKeys.cloneNode(true);
        confirmDeleteAllApiKeys.parentNode.replaceChild(newConfirmBtn, confirmDeleteAllApiKeys);

        // Add new event listener
        newConfirmBtn.addEventListener('click', function() {
            deleteAllApiKeys();
            try {
                const bsModal = bootstrap.Modal.getInstance(modalElement);
                if (bsModal) {
                    bsModal.hide();
                }
            } catch (error) {
                console.error('Error hiding modal:', error);
                modalElement.classList.remove('show');
                modalElement.style.display = 'none';
                document.body.classList.remove('modal-open');
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) backdrop.remove();
            }
        }, { once: true });
    }

    // Set up cancel button
    const cancelDeleteAllApiKeys = document.getElementById('cancelDeleteAllApiKeys');
    if (cancelDeleteAllApiKeys) {
        // Remove any existing event listeners
        const newCancelBtn = cancelDeleteAllApiKeys.cloneNode(true);
        cancelDeleteAllApiKeys.parentNode.replaceChild(newCancelBtn, cancelDeleteAllApiKeys);

        // Add new event listener
        newCancelBtn.addEventListener('click', function() {
            try {
                const bsModal = bootstrap.Modal.getInstance(modalElement);
                if (bsModal) {
                    bsModal.hide();
                }
            } catch (error) {
                console.error('Error hiding modal:', error);
                modalElement.classList.remove('show');
                modalElement.style.display = 'none';
                document.body.classList.remove('modal-open');
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) backdrop.remove();
            }
        }, { once: true });
    }

    // Show the modal
    try {
        // Make sure Bootstrap is available
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            // Create and show the modal
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } else {
            console.error('Bootstrap Modal is not available');
            // Fallback to confirm dialog
            if (confirm('Are you sure you want to delete all API keys? This action cannot be undone.')) {
                deleteAllApiKeys();
            }
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        // Fallback to confirm dialog
        if (confirm('Are you sure you want to delete all API keys? This action cannot be undone.')) {
            deleteAllApiKeys();
        }
    }
}

function deleteAllApiKeys() {
    if (!window.apiKeys || window.apiKeys.length === 0) {
        showToast('No API keys to delete', 'info');
        return;
    }

    // Clear all API keys
    window.apiKeys = [];

    // Clear the current API key
    localStorage.removeItem('csvision_api_key');

    // Save and update UI
    saveApiKeys();
    updateApiKeysList();

    // Clear input fields
    clearApiKeyInputs();

    showToast('All API keys deleted', 'success');
}

function updateApiKeysList() {
    const apiKeysList = document.getElementById('apiKeysList');
    const deleteApiKeyBtn = document.getElementById('deleteApiKeyBtn');
    const apiKeysCount = document.getElementById('apiKeysCount');
    if (!apiKeysList) return;

    // Store the current selection if any
    const currentSelection = apiKeysList.value;

    // Clear the dropdown
    apiKeysList.innerHTML = '';

    // Update API keys count
    if (apiKeysCount) {
        const count = window.apiKeys ? window.apiKeys.length : 0;
        apiKeysCount.textContent = count + (count === 1 ? ' key' : ' keys');
    }

    // If no API keys, show a message and disable delete button
    if (!window.apiKeys || window.apiKeys.length === 0) {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = 'No API keys saved';
        option.disabled = true;
        option.selected = true;
        apiKeysList.appendChild(option);

        if (deleteApiKeyBtn) {
            deleteApiKeyBtn.disabled = true;
        }
        return;
    }

    // Enable delete button if we have keys
    if (deleteApiKeyBtn) {
        deleteApiKeyBtn.disabled = false;
    }

    // Add each API key to the dropdown
    let selectedIndex = -1;
    window.apiKeys.forEach((apiKey, index) => {
        const option = document.createElement('option');
        option.value = index.toString();
        option.textContent = apiKey.name + ' (••••' + apiKey.key.slice(-4) + ')';

        // Mark active key with an asterisk
        if (apiKey.active) {
            option.textContent = '✓ ' + option.textContent;
            selectedIndex = index;
        }

        apiKeysList.appendChild(option);
    });

    // Set the selection
    if (selectedIndex >= 0) {
        apiKeysList.value = selectedIndex.toString();
    } else if (currentSelection && apiKeysList.querySelector(`option[value="${currentSelection}"]`)) {
        apiKeysList.value = currentSelection;
    }

    // Add event listener to the dropdown for changing the active key
    apiKeysList.onchange = function() {
        const selectedIndex = parseInt(this.value);
        if (!isNaN(selectedIndex) && selectedIndex >= 0 && selectedIndex < window.apiKeys.length) {
            // Set as active key
            setActiveApiKey(selectedIndex);

            // Display the selected key details in the input fields
            displayApiKeyDetails(selectedIndex);
        }
    };

    // If we have an active key, display its details
    const activeKeyIndex = window.apiKeys.findIndex(key => key.active);
    if (activeKeyIndex >= 0) {
        displayApiKeyDetails(activeKeyIndex);
    } else {
        // Clear the input fields if no active key
        clearApiKeyInputs();
    }
}

function displayApiKeyDetails(index) {
    if (index < 0 || index >= window.apiKeys.length) {
        console.error('Invalid API key index:', index);
        return;
    }

    const apiKey = window.apiKeys[index];
    const apiKeyNameInput = document.getElementById('apiKeyName');
    const apiKeyInput = document.getElementById('apiKey');
    const addApiKeyBtn = document.getElementById('addApiKeyBtn');
    const cancelEditBtn = document.getElementById('cancelEditBtn');

    if (apiKeyNameInput) apiKeyNameInput.value = apiKey.name;
    if (apiKeyInput) apiKeyInput.value = apiKey.key;

    // Change the button text to "Update"
    if (addApiKeyBtn) {
        addApiKeyBtn.textContent = 'Update';
        addApiKeyBtn.dataset.mode = 'update';
        addApiKeyBtn.dataset.index = index.toString();
    }

    // Show the cancel button
    if (cancelEditBtn) {
        cancelEditBtn.style.display = 'inline-block';
    }
}

function clearApiKeyInputs() {
    const apiKeyNameInput = document.getElementById('apiKeyName');
    const apiKeyInput = document.getElementById('apiKey');
    const addApiKeyBtn = document.getElementById('addApiKeyBtn');
    const cancelEditBtn = document.getElementById('cancelEditBtn');

    if (apiKeyNameInput) apiKeyNameInput.value = '';
    if (apiKeyInput) apiKeyInput.value = '';

    // Change the button text back to "Add"
    if (addApiKeyBtn) {
        addApiKeyBtn.textContent = 'Add';
        addApiKeyBtn.dataset.mode = 'add';
        delete addApiKeyBtn.dataset.index;
    }

    // Hide the cancel button
    if (cancelEditBtn) {
        cancelEditBtn.style.display = 'none';
    }
}

function exportApiKeys() {
    if (!window.apiKeys || window.apiKeys.length === 0) {
        showToast('No API keys to export', 'error');
        return;
    }

    // Create a copy of the API keys without the active flag for security
    const keysToExport = window.apiKeys.map(key => ({
        name: key.name,
        key: key.key,
        dateAdded: key.dateAdded
    }));

    // Create a JSON file
    const content = JSON.stringify(keysToExport, null, 2);
    const blob = new Blob([content], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    // Create a download link
    const a = document.createElement('a');
    a.href = url;
    a.download = 'csvision_api_keys.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showToast('API keys exported successfully', 'success');
}

function importApiKeys(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importedKeys = JSON.parse(e.target.result);

            if (!Array.isArray(importedKeys)) {
                throw new Error('Invalid format: Expected an array of API keys');
            }

            // Validate each key
            const validKeys = importedKeys.filter(key =>
                key && typeof key === 'object' &&
                typeof key.name === 'string' &&
                typeof key.key === 'string'
            );

            if (validKeys.length === 0) {
                throw new Error('No valid API keys found in the file');
            }

            // Add each valid key if it doesn't already exist
            let addedCount = 0;
            validKeys.forEach(key => {
                // Check if this key already exists
                const existingKeyIndex = window.apiKeys.findIndex(k => k.key === key.key);
                if (existingKeyIndex < 0) {
                    // Add the key (not active by default)
                    window.apiKeys.push({
                        name: key.name,
                        key: key.key,
                        active: false,
                        dateAdded: key.dateAdded || new Date().toISOString()
                    });
                    addedCount++;
                }
            });

            // If we have no active key and just imported some, make the first one active
            if (!window.apiKeys.some(k => k.active) && window.apiKeys.length > 0) {
                window.apiKeys[0].active = true;
                localStorage.setItem('csvision_api_key', window.apiKeys[0].key);
            }

            // Save and update UI
            saveApiKeys();
            updateApiKeysList();

            if (addedCount > 0) {
                showToast(`Imported ${addedCount} new API key(s)`, 'success');
            } else {
                showToast('No new API keys were imported', 'info');
            }
        } catch (error) {
            console.error('Import error:', error);
            showToast('Failed to import API keys: ' + error.message, 'error');
        }
    };
    reader.readAsText(file);

    // Clear the file input so the same file can be selected again
    event.target.value = '';
}

window.initSettingsManager = initSettingsManager;
window.saveSettings = saveSettings;
window.loadSettings = loadSettings;
window.showTemplateImport = showTemplateImport;
window.saveTemplates = saveTemplates;
window.loadTemplates = loadTemplates;
window.updateTemplateButtons = updateTemplateButtons;
window.getApiKey = getApiKey;

// API Keys functions
window.loadApiKeys = loadApiKeys;
window.saveApiKeys = saveApiKeys;
window.addApiKey = addApiKey;
window.setActiveApiKey = setActiveApiKey;
window.deleteApiKey = deleteApiKey;
window.showDeleteApiKeyModal = showDeleteApiKeyModal;
window.ensureDeleteApiKeyModal = ensureDeleteApiKeyModal;
window.deleteAllApiKeys = deleteAllApiKeys;
window.showDeleteAllApiKeysModal = showDeleteAllApiKeysModal;
window.ensureDeleteAllApiKeysModal = ensureDeleteAllApiKeysModal;
window.updateApiKeysList = updateApiKeysList;
window.exportApiKeys = exportApiKeys;
window.importApiKeys = importApiKeys;
window.markApiKeyAsFailed = markApiKeyAsFailed;
window.isApiKeyFailed = isApiKeyFailed;
window.getNextApiKey = getNextApiKey;

// Legacy prompt getters
window.getPositivePrompt = getPositivePrompt;
window.getNegativePrompt = getNegativePrompt;

// Detailed prompt getters
window.getPositiveTitlePrompt = getPositiveTitlePrompt;
window.getNegativeTitlePrompt = getNegativeTitlePrompt;
window.getPositiveDescriptionPrompt = getPositiveDescriptionPrompt;
window.getNegativeDescriptionPrompt = getNegativeDescriptionPrompt;
window.getPositiveKeywordsPrompt = getPositiveKeywordsPrompt;
window.getNegativeKeywordsPrompt = getNegativeKeywordsPrompt;

// Length getters
window.getMaxTitleLength = getMaxTitleLength;
window.getMaxDescriptionLength = getMaxDescriptionLength;
window.getMaxKeywordsFromSettings = getMaxKeywordsFromSettings;

function deleteTemplate(index) {
    // csvTemplates is a global variable defined in csv-manager.js
    if (window.csvTemplates) {
        const templateName = window.csvTemplates[index]?.name || 'Unknown';
        window.csvTemplates.splice(index, 1);
        saveTemplates();
        updateTemplateList();
        updateTemplateButtons();
        showToast(`Template "${templateName}" deleted successfully`);
    }
}